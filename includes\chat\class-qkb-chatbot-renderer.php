<?php
/**
 * QKB Chatbot Renderer
 *
 * Handles rendering of the floating chatbot
 */

if (!defined('ABSPATH')) {
    exit;
}

require_once QKB_PLUGIN_DIR . 'includes/chat/class-qkb-chat-renderer.php';

class QKB_Chatbot_Renderer extends QKB_Chat_Renderer
{
    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Initialize settings for the floating chatbot
     */
    protected function init_settings()
    {
        $this->settings = [
            'chatbot_name' => get_option('qkb_chatbot_name', 'Ask Q'),
            'chatbot_icon' => get_option('qkb_chatbot_icon', QKB_PLUGIN_URL . 'assets/images/q.svg'),
            'splash_subtitle' => get_option('qkb_splash_subtitle', 'Your AI Assistant'),
            'default_assistant_id' => get_option('qkb_default_chatbot_assistant', 0),
            'quick_access_buttons' => get_option('qkb_quick_access_buttons', []),
        ];
    }

    /**
     * Get the selected default assistant
     *
     * @return object|false WP_Term object or false
     */
    protected function get_selected_assistant()
    {
        $assistant_id = $this->settings['default_assistant_id'];

        if ($assistant_id > 0) {
            $assistant = get_term($assistant_id, 'kb_assistant');
            if (!is_wp_error($assistant) && $assistant) {
                return $assistant;
            }
        }

        // Fallback to default assistant
        return $this->get_default_assistant();
    }

    /**
     * Render the floating chatbot button
     */
    public function render_button()
    {
        $chatbot_name = $this->settings['chatbot_name'];
        $chatbot_icon = $this->settings['chatbot_icon'];
        ?>
        <div class="qi-chat-button qkb-chat-button" role="button" aria-label="Open Chat">
            <div class="qi-chat-branding qkb-chat-branding">
                <div class="qi-bot-avatar qkb-bot-avatar">
                    <img src="<?php echo esc_url($chatbot_icon); ?>" alt="<?php echo esc_attr($chatbot_name); ?>"
                        class="qi-avatar qi-default-avatar qkb-avatar qkb-default-avatar">
                </div>
                <span class="qi-chat-button-text qkb-chat-button-text"><?php echo esc_html($chatbot_name); ?></span>
            </div>

            <div>
                <button class="qi-chatbot-control-button qi-expand-button qkb-chatbot-control-button qkb-expand-button"
                    title="Expand">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                        stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="18 15 12 9 6 15" />
                        </polyline>
                    </svg>
                </button>
            </div>
        </div>
        <?php
    }

    /**
     * Render the chatbot interface
     */
    public function render()
    {
        $selected_assistant = $this->get_selected_assistant();
        $chatbot_name = $this->settings['chatbot_name'];
        $chatbot_icon = $this->settings['chatbot_icon'];
        ?>
        <div class="qi-chatbot-container qkb-chatbot-container">
            <div class="qi-chatbot-header qkb-chatbot-header">
                <div class="qi-chatbot-branding qkb-chatbot-branding">
                    <div class="qi-bot-avatar qkb-bot-avatar">
                        <img src="<?php echo esc_url($chatbot_icon); ?>" alt="<?php echo esc_attr($chatbot_name); ?>"
                            class="qi-avatar qi-default-avatar qkb-avatar qkb-default-avatar">
                    </div>
                    <div class="qi-bot-info qkb-bot-info">
                        <div class="qi-bot-name qkb-bot-name"><?php echo esc_html($chatbot_name); ?></div>
                        <div class="qkb-assistant-name">
                            <?php echo esc_html($selected_assistant->name); ?>
                        </div>
                    </div>
                </div>
                <div class="qi-chatbot-controls qkb-chatbot-controls">
                    <button class="qi-chatbot-control-button qi-new-chat-button qkb-chatbot-control-button qkb-new-chat-button"
                        title="New Chat">
                        New Chat
                    </button>
                    <button class="qi-chatbot-control-button qi-maximize-button qkb-chatbot-control-button qkb-maximize-button"
                        title="Maximize">
                        <svg class="maximize-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                            stroke-linecap="round" stroke-linejoin="round">
                            <polyline points="15 3 21 3 21 9" />
                            <polyline points="9 21 3 21 3 15" />
                            <line x1="21" y1="3" x2="14" y2="10" />
                            <line x1="3" y1="21" x2="10" y2="14" />
                        </svg>
                        <svg class="restore-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                            stroke-linecap="round" stroke-linejoin="round">
                            <polyline points="4 14 10 14 10 20" />
                            <polyline points="20 10 14 10 14 4" />
                            <line x1="14" y1="10" x2="21" y2="3" />
                            <line x1="3" y1="21" x2="10" y2="14" />
                        </svg>
                    </button>
                    <button class="qi-chatbot-control-button qi-minimize-button qkb-chatbot-control-button qkb-minimize-button"
                        title="Minimize">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                            stroke-linecap="round" stroke-linejoin="round">
                            <polyline points="6 9 12 15 18 9" />
                        </svg>
                    </button>
                </div>
            </div>
            <div class="qi-chatbot-messages qkb-chatbot-messages">

            </div>

            <?php $this->render_quick_access_buttons(); ?>
            <div class="qi-input-container" role="form" aria-label="Chat Input">
                <div class="qi-input-area" role="textbox">
                    <textarea class="qkb-input-textarea" placeholder="How can I help you today?..."
                        aria-label="<?php esc_attr_e('Chat input', 'q-knowledge-base'); ?>" maxlength="500" autocomplete="off"
                        spellcheck="true"></textarea>
                </div>

                <div class="qi-input-buttons">
                    <div class="qi-utility-buttons">
                        <button class="qkb-quick-access-utility-button"
                            title="<?php esc_attr_e('Quick Access', 'q-knowledge-base'); ?>">
                            <i class="fas fa-bolt"></i>
                        </button>
                    </div>

                    <div class="qi-submit-buttons">
                        <div class="qi-character-count qi-character-count-inline" aria-live="polite">
                            <span class="qi-current-count">500</span>
                        </div>
                        <button class="qi-send-button" disabled>
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <line x1="22" y1="2" x2="11" y2="13"></line>
                                <polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>





            <div class="qkb-new-chat-modal">
                <div class="qkb-modal-overlay"></div>
                <div class="qkb-modal-container">
                    <div class="qkb-modal-content-chat">
                        <h2>Start New Chat?</h2>
                        <p>This will clear your current conversation.</p>
                    </div>
                    <div class="qkb-modal-actions-chat">
                        <button class="qkb-cancel-new-chat">Cancel</button>
                        <button class="qkb-confirm-new-chat">Start New Chat</button>

                    </div>
                </div>
            </div>

            <!-- Quick Access Content Modal -->
            <div class="qkb-quick-access-modal">
                <div class="qkb-modal-overlay"></div>
                <div class="qkb-modal-container">
                    <div class="qkb-modal-header">
                        <h2 class="qkb-modal-title"></h2>
                        <button class="qkb-modal-close">&times;</button>
                    </div>
                    <div class="qkb-modal-content"></div>
                </div>
            </div>
        </div>

        <div class="qi-backdrop qkb-backdrop"></div>
        <?php
        $this->render_message_templates();
        $this->render_suggestion_buttons_template($selected_assistant);
        ?>

        <!-- Direct script tag for particles.js to ensure it's loaded -->
        <script src="https://cdn.jsdelivr.net/particles.js/2.0.0/particles.min.js"></script>

        <?php
    }

    /**
     * Render suggestion buttons template
     *
     * @param object $assistant WP_Term object for the assistant
     * @return void
     */
    protected function render_suggestion_buttons_template($assistant)
    {
        // Get suggested prompts from the assistant
        $suggested_prompts = function_exists('get_term_meta') ? get_term_meta($assistant->term_id, 'assistant_suggested_prompts', true) : [];

        // If no suggested prompts, use default ones
        if (empty($suggested_prompts) || !is_array($suggested_prompts)) {
            $suggested_prompts = [
                'What can you help me with?',
                'How do I get started?',
                'Tell me about your features'
            ];
        }

        // Get assistant actions from the assistant
        $assistant_actions = function_exists('get_term_meta') ? get_term_meta($assistant->term_id, 'assistant_actions', true) : [];

        // If no assistant actions, initialize as empty array
        if (empty($assistant_actions) || !is_array($assistant_actions)) {
            $assistant_actions = [];
        }

        // Process shortcodes in assistant actions content
        foreach ($assistant_actions as &$action) {
            if (isset($action['content'])) {
                // Debug log before processing
                error_log('Assistant Action before shortcode processing: ' . $action['name'] . ' - Content: ' . $action['content']);

                $action['content'] = do_shortcode($action['content']);

                // Debug log after processing
                error_log('Assistant Action after shortcode processing: ' . $action['name'] . ' - Content: ' . $action['content']);
            }
        }

        // Create an array to store assistant actions by assistant ID
        $assistant_actions_by_id = [];
        $assistant_actions_by_id[$assistant->term_id] = $assistant_actions;

        // Convert to JSON for JavaScript
        $suggested_prompts_json = function_exists('wp_json_encode') ? wp_json_encode($suggested_prompts) : json_encode($suggested_prompts);
        $assistant_actions_json = function_exists('wp_json_encode') ? wp_json_encode($assistant_actions_by_id) : json_encode($assistant_actions_by_id);
        ?>
        <script type="text/javascript">
            // Add suggested prompts and assistant actions to the qkbChatbot object
            if (typeof qkbChatbot !== 'undefined') {
                qkbChatbot.suggested_prompts = <?php echo $suggested_prompts_json; ?>;
                qkbChatbot.assistant_actions = <?php echo $assistant_actions_json; ?>;
            }
        </script>
        <?php
    }

    /**
     * Render quick access buttons
     *
     * @return void
     */
    protected function render_quick_access_buttons()
    {
        $quick_access_buttons = $this->settings['quick_access_buttons'];

        // If no quick access buttons, return early
        if (empty($quick_access_buttons)) {
            return;
        }

        ?>
        <div class="qkb-quick-access-container">
            <div class="qkb-quick-access-buttons-wrapper">
                <div class="qkb-quick-access-buttons">
                    <?php foreach ($quick_access_buttons as $button):
                        // Process shortcodes in the content
                        $processed_content = do_shortcode($button['content']);

                        // Create a unique ID for this button's content
                        $content_id = 'qkb_content_' . md5($processed_content . uniqid());
                        ?>
                        <button class="qkb-quick-access-button" data-content-id="<?php echo esc_attr($content_id); ?>"
                            data-show-modal="<?php echo isset($button['show_in_modal']) && $button['show_in_modal'] ? '1' : '0'; ?>"
                            data-modal-title="<?php echo isset($button['modal_title']) ? esc_attr($button['modal_title']) : esc_attr($button['name']); ?>">
                            <i class="<?php echo esc_attr($button['icon']); ?>"></i>
                            <span><?php echo esc_html($button['name']); ?></span>
                        </button>
                        <!-- Hidden div to store the actual content -->
                        <div id="<?php echo esc_attr($content_id); ?>" class="qkb-hidden-content" style="display: none;">
                            <?php echo $processed_content; ?>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
        <?php
    }
}
