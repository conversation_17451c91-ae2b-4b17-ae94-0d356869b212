jQuery(document).ready(function($) {
    'use strict';

    // Remove or comment out the auto initialization:
    // try {
    //     new ChatBot();
    // } catch (error) {
    //     console.error('Chat initialization error:', error);
    // }

    // Function to detect SVG images and apply the appropriate class
    function detectAndClassifySvgImages() {
        // Find all images in bot avatars, welcome icons, splash logos, and primary logos
        $('.qkb-bot-avatar img, .qkb-splash-logo img, .qi-primary-logo img').each(function() {
            var $img = $(this);
            var imgSrc = $img.attr('src');

            // Check if the image is an SVG
            if (imgSrc && imgSrc.toLowerCase().endsWith('.svg')) {
                // Add the SVG class
                $img.addClass('qkb-svg-image');
            } else {
                // Remove the SVG class if it's not an SVG
                $img.removeClass('qkb-svg-image');
            }
        });
    }

    // Run the detection on page load
    detectAndClassifySvgImages();

    // Set up a MutationObserver to detect when images are added or changed
    var observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList' || mutation.type === 'attributes') {
                detectAndClassifySvgImages();
            }
        });
    });

    // Start observing the document with the configured parameters
    observer.observe(document.body, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['src']
    });

    // Flag to track whether the chat interface has been loaded.
    var chatBotLoaded = false;

    // When the chat button is clicked, load the chat interface via AJAX if not already loaded.
    $(document).on('click', '.qi-chat-button, .qkb-chat-button', function(e) {
        e.preventDefault();
        if (!chatBotLoaded) {
            // Get AJAX URL and nonce from global object or use defaults
            const ajaxUrl = (typeof qkbChatbot !== 'undefined' && qkbChatbot?.ajax_url) || '/wp-admin/admin-ajax.php';
            const nonce = (typeof qkbChatbot !== 'undefined' && qkbChatbot?.nonce) || '';

            $.ajax({
                url: ajaxUrl,
                type: 'POST',
                data: {
                    action: 'qkb_load_chatbot',
                    nonce: nonce
                },
                success: function(response) {
                    if (response.success) {
                        // Append the full chat UI markup to the body.
                        $('body').append(response.data);
                        try {
                            // Initialize the ChatBot after the interface is loaded.
                            new ChatBot();
                            chatBotLoaded = true;
                            // Apply SVG detection after chat is loaded
                            detectAndClassifySvgImages();
                            // Trigger events for chat enhancements (both qi and qkb prefixes for compatibility)
                            $(document).trigger('qi-chat-loaded');
                            $(document).trigger('qkb-chat-loaded');
                        } catch (error) {
                            console.error('Chat initialization error:', error);
                        }
                    }
                },
                error: function() {
                    console.error('Failed to load chat interface.');
                }
            });
        } else {
            // If already loaded, simply toggle visibility.
            $('.qi-chatbot-container, .qkb-chatbot-container').toggleClass('visible');
        }
    });

    class ChatBot {
        constructor() {
            // Prevent multiple instances
            if (window.qkbChatBotInstance) {
                console.log('ChatBot instance already exists, returning existing instance');
                return window.qkbChatBotInstance;
            }

            // Use the selected assistant ID from the settings if available, otherwise use default
            this.currentAssistant = (typeof qkbChatbot !== 'undefined' && qkbChatbot?.selectedassistant) || 0;

            // Default assistant ID
            const defaultAssistantId = (typeof qkbChatbot !== 'undefined' && qkbChatbot?.defaultassistant) || 0;

            // Flag to track if we're using the default assistant
            this.isUsingDefaultAssistant = this.currentAssistant === defaultAssistantId;

            this.$button = $('.qi-chat-button, .qkb-chat-button');
            this.$container = $('.qi-chatbot-container, .qkb-chatbot-container');
            this.$messages = $('.qi-chatbot-messages, .qkb-chatbot-messages');
            this.$input = $('.qi-input-area textarea');
            this.$sendButton = $('.qi-send-button');

            this.$characterCount = $('.qi-current-count');

            this.username = (typeof qkbChatbot !== 'undefined' && qkbChatbot?.user_name) || 'there';

            // Get chatbot info
            const chatbotName = (typeof qkbChatbot !== 'undefined' && qkbChatbot?.chatbot_name) || 'Ask Q';
            const chatbotIcon = (typeof qkbChatbot !== 'undefined' && qkbChatbot?.chatbot_icon) ||
                ((typeof qkbChatbot !== 'undefined' && qkbChatbot?.plugin_url) ?
                qkbChatbot.plugin_url + '/assets/images/q.svg' : '/assets/images/q.svg');
            const splashSubtitle = (typeof qkbChatbot !== 'undefined' && qkbChatbot?.splash_subtitle) || 'Your AI Assistant';

            this.templates = {
                userMessage: $('#qkb-user-message-template').html(),
                botMessage: $('#qkb-bot-message-template').html(),
                errorMessage: $('#qkb-error-message-template').html(),
                splashScreen: `
                    <div class="qkb-splash-screen" role="alert" aria-live="assertive" aria-busy="true">
                        <div class="qkb-splash-circles">
                            <div class="qkb-splash-circle"></div>
                            <div class="qkb-splash-circle"></div>
                            <div class="qkb-splash-circle"></div>
                        </div>
                        <div class="qkb-splash-content">
                            <div class="qkb-splash-logo">
                                <img src="${chatbotIcon}" alt="${chatbotName} Logo">
                            </div>
                            <h1 class="qkb-splash-title">${chatbotName}</h1>
                            <p class="qkb-splash-subtitle">${splashSubtitle}</p>
                            <div class="qkb-splash-loader">
                                <div class="qkb-splash-loader-bar"></div>
                            </div>
                        </div>
                        <div class="qkb-splash-powered">
                            <span>Powered by Q-Ai</span>
                        </div>
                    </div>
                `,
                welcomeScreen: function() {
                    // Default suggested prompts
                    const defaultPrompts = [
                        'What can you help me with?',
                        'How do I get started?',
                        'Tell me about your features'
                    ];

                    // Get suggested prompts from the qkbChatbot object or use defaults
                    const suggestedPrompts = (typeof qkbChatbot !== 'undefined' && qkbChatbot?.suggested_prompts) || defaultPrompts;

                    // Generate suggestion buttons HTML
                    let suggestionButtonsHtml = '';
                    suggestedPrompts.forEach(prompt => {
                        suggestionButtonsHtml += `<button class="qkb-suggestion-button">${prompt}</button>`;
                    });

                    return `
                    <div class="qkb-welcome-screen" role="region" aria-label="Welcome Screen">
                        <div class="qkb-welcome-particles" id="qkb-particles-js"></div>
                        <div class="qkb-welcome-content">
                            <div class="qkb-welcome-header">
                                <h2 class="qkb-welcome-heading">Hello, ${this.username}</h2>
                                <p class="qkb-welcome-subheading">How can I assist you today?</p>
                            </div>
                            <div class="qkb-welcome-suggestions">
                                <h3 class="qkb-suggestions-title">Try asking:</h3>
                                <div class="qkb-suggestion-buttons">
                                    ${suggestionButtonsHtml}
                                </div>
                            </div>
                        </div>
                    </div>
                    `;
                }
            };

            this.initializeEventListeners();


            this.loadChatHistory();

            this.setupFeedbackHandlers();

            this.isTyping = false;
            this.typingTimeout = null;
            this.maxMessageLength = 500;
            this.currentSuggestionIndex = -1;
            this.suggestionTimeout = null;
            this.minQueryLength = 2;

            this.messageHistory = [];
            this.suggestionPatterns = new Map();
            this.minPatternLength = 2;
            this.maxPatternLength = 5;

            this.$backdrop = $('<div class="qkb-backdrop"></div>').appendTo('body');

            // Add window resize event listener for responsive behavior
            $(window).on('resize', () => this.handleResize());

            this.showSplashScreen();

            // Add ARIA attributes to main container
            this.$container.attr({
                'role': 'dialog',
                'aria-label': 'Chat with Ask Q',
                'aria-modal': 'true'
            });

            // Add ARIA live region for messages
            this.$messages.attr({
                'role': 'log',
                'aria-live': 'polite',
                'aria-atomic': 'false'
            });

            // Enhance input accessibility
            this.$input.attr({
                'role': 'textbox',
                'aria-label': 'Chat message',
                'aria-multiline': 'true'
            });

            // Add ARIA labels to buttons
            this.$sendButton.attr('aria-label', 'Send message');
            $('.qkb-minimize-button').attr('aria-label', 'Minimize chat');
            $('.qkb-maximize-button').attr('aria-label', 'Maximize chat');
            $('.qkb-new-chat-button').attr('aria-label', 'Start new chat');

            // Initialize keyboard navigation
            this.initializeKeyboardNavigation();



            this.mlPatterns = new Map();
            this.responseCache = new Map();
            this.interactionHistory = [];

            // Initialize ML patterns after a short delay
            setTimeout(() => {
                this.loadMLPatterns().catch(error => {
                    console.warn('ML patterns initialization error:', error);
                });
            }, 1000);



            // Check if we're in full page mode
            this.isFullPage = this.$container.closest('.qkb-fullpage-wrapper').length > 0;

            if (this.isFullPage) {
                // Show chat interface immediately for full page
                this.$container.addClass('visible');
                this.showWelcomeScreen();

                // Adjust container size on window resize
                $(window).on('resize', () => {
                    this.adjustFullPageSize();
                });

                // Initial size adjustment
                this.adjustFullPageSize();
            }

            // Store this instance globally to prevent duplicates
            window.qkbChatBotInstance = this;
        }

        initializeEventListeners() {
            this.$button.on('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.openChat();
            });

            $('.qi-minimize-button, .qkb-minimize-button').on('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.minimizeChat();
            });

            $('.qi-maximize-button, .qkb-maximize-button').on('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.toggleMaximize();
            });

            this.$input.on('input', () => this.handleInputChange());

            this.$sendButton.on('click', () => this.sendMessage());

            this.$input.on('keypress', (e) => {
                if (e.which === 13 && !e.shiftKey) {
                    e.preventDefault();
                    if (!this.$sendButton.prop('disabled')) {
                        this.sendMessage();
                    }
                }
            });



            // File attachment functionality has been removed

            $('.qi-new-chat-button, .qkb-new-chat-button').on('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.startNewChat();
            });

            // Quick Access Utility Button
            $('.qi-quick-access-utility-button, .qkb-quick-access-utility-button').on('click', (e) => {
                e.preventDefault();
                const $utilityButton = $(e.currentTarget);
                const $wrapper = $('.qi-quick-access-buttons-wrapper, .qkb-quick-access-buttons-wrapper');

                // Simply toggle the visibility of the wrapper and active state of the button
                if ($utilityButton.hasClass('active')) {
                    $wrapper.removeClass('visible');
                    $utilityButton.removeClass('active');
                } else {
                    $wrapper.addClass('visible');
                    $utilityButton.addClass('active');
                }
            });

            // Quick Access Toggle Button (if it exists in the DOM)
            $(document).on('click', '.qi-quick-access-toggle-button, .qkb-quick-access-toggle-button', (e) => {
                e.preventDefault();
                const $button = $(e.currentTarget);
                const $wrapper = $('.qi-quick-access-buttons-wrapper, .qkb-quick-access-buttons-wrapper');
                const $utilityButton = $('.qi-quick-access-utility-button, .qkb-quick-access-utility-button');

                // Toggle visibility based on current state
                if ($wrapper.hasClass('visible')) {
                    if ($button.attr('aria-expanded')) {
                        $button.attr('aria-expanded', 'false');
                    }
                    $wrapper.removeClass('visible');
                    $utilityButton.removeClass('active');
                } else {
                    if ($button.attr('aria-expanded')) {
                        $button.attr('aria-expanded', 'true');
                    }
                    $wrapper.addClass('visible');
                    $utilityButton.addClass('active');
                }
            });

            $(document).on('click', '.qi-quick-access-button, .qkb-quick-access-button', (e) => {
                e.preventDefault();
                const $button = $(e.currentTarget);
                const contentId = $button.data('content-id');
                const showInModal = $button.data('show-modal') === 1;
                const buttonName = $button.find('span').text();

                if (contentId) {
                    // Get the content from the hidden div
                    const content = $('#' + contentId).html();

                    if (content) {
                        if (showInModal) {
                            // Show content in modal
                            const $modal = $('.qkb-quick-access-modal');
                            const modalTitle = $button.data('modal-title') || buttonName;

                            $modal.find('.qkb-modal-title').text(modalTitle);
                            $modal.find('.qkb-modal-content').html(content);
                            $modal.addClass('active');

                            // Add event listener for close button if not already added
                            if (!this.modalEventsInitialized) {
                                $(document).on('click', '.qkb-quick-access-modal .qkb-modal-close, .qkb-quick-access-modal .qkb-modal-overlay', () => {
                                    $('.qkb-quick-access-modal').removeClass('active');
                                });

                                // Prevent clicks on modal content from closing the modal
                                $(document).on('click', '.qkb-quick-access-modal .qkb-modal-content', (e) => {
                                    e.stopPropagation();
                                });

                                this.modalEventsInitialized = true;
                            }
                        } else {
                            // Add the content as a bot message with rich text formatting
                            // This content includes processed shortcodes from WordPress
                            this.addMessage(content, false, 'message', true, false, true);
                        }

                        // Close the quick access buttons
                        $('.qi-quick-access-buttons-wrapper, .qkb-quick-access-buttons-wrapper').removeClass('visible');
                        $('.qi-quick-access-utility-button, .qkb-quick-access-utility-button').removeClass('active');
                    }
                }
            });
        }

        openChat() {
            if (this.isFullPage) return;

            // Show container with animation
            this.$container.addClass('visible');
            this.$button.fadeOut(300);
            this.$backdrop.removeClass('active');

            // Handle responsive adjustments
            this.handleResize();

            // Check if we're on mobile and apply full screen mode
            if (window.innerWidth <= 480) {
                // Add mobile fullscreen class to body to prevent scrolling
                $('body').addClass('qkb-mobile-fullscreen-active');
            }

            const hasMessages = this.$messages.find('.qkb-message').length > 0;

            if (!hasMessages) {
                // Show splash screen only if no messages
                this.showSplashScreen();
            }

            // Initialize the chat immediately if there are messages
            // or after splash animation completes
            const initializeDelay = hasMessages ? 0 : 4500;

            setTimeout(() => {
                // Show welcome screen if no messages
                if (!hasMessages) {
                    this.showWelcomeScreen();
                }

                this.$input.focus();
                this.scrollToBottom();
            }, initializeDelay);
        }

        minimizeChat() {
            if (this.isFullPage) return;

            // Remove mobile fullscreen class from body
            $('body').removeClass('qkb-mobile-fullscreen-active qi-mobile-fullscreen-active qkb-maximized-active');

            this.$container.removeClass('visible maximized');
            setTimeout(() => {
                this.$button.fadeIn(300);
                this.$backdrop.removeClass('active');
            }, 300); // Wait for the animation to complete
        }

        toggleMaximize() {
            if (this.isFullPage) return;

            const isMaximized = this.$container.hasClass('maximized');
            const isMobile = window.innerWidth <= 768;

            if (isMaximized) {
                // Restore to normal size
                this.$container.removeClass('maximized');
                this.$container.find('.qkb-maximize-button').attr('title', 'Maximize');

                // Remove body scroll lock only on mobile
                if (isMobile) {
                    $('body').removeClass('qkb-maximized-active');
                }
            } else {
                // Maximize the chatbot to 1/3 of screen on right side
                this.$container.addClass('maximized');
                this.$container.find('.qkb-maximize-button').attr('title', 'Restore');

                // Add body scroll lock only on mobile (where it becomes full screen)
                if (isMobile) {
                    $('body').addClass('qkb-maximized-active');
                }
            }

            // Adjust message container height after toggling maximize
            setTimeout(() => {
                this.adjustMessageContainerHeight();
                this.scrollToBottom();
            }, 100);
        }

        // Handle window resize events
        handleResize() {
            // Adjust container based on screen size
            const windowWidth = window.innerWidth;
            const isMaximized = this.$container.hasClass('maximized');

            // Apply different styles based on screen width
            if (windowWidth <= 480) {
                // Mobile view - full screen
                this.$container.addClass('qi-mobile-view qkb-mobile-view');

                // Hide the body scroll when in mobile full screen
                if (!isMaximized) {
                    $('body').addClass('qi-mobile-fullscreen-active qkb-mobile-fullscreen-active');
                }
            } else {
                // Desktop view
                this.$container.removeClass('qi-mobile-view qkb-mobile-view');
                if (!isMaximized) {
                    $('body').removeClass('qi-mobile-fullscreen-active qkb-mobile-fullscreen-active');
                }
            }

            // Adjust message container height for better responsiveness
            this.adjustMessageContainerHeight();
        }

        // Adjust message container height
        adjustMessageContainerHeight() {
            if (!this.$container.hasClass('visible')) return;

            const containerHeight = this.$container.height();
            const headerHeight = this.$container.find('.qi-chatbot-header, .qkb-chatbot-header').outerHeight() || 0;
            const inputHeight = this.$container.find('.qi-input-container, .qkb-input-container').outerHeight() || 0;

            // Calculate available height for messages
            const messagesHeight = containerHeight - headerHeight - inputHeight;

            // Set the height of the messages container
            if (messagesHeight > 0) {
                this.$messages.css('height', messagesHeight + 'px');
            }
        }

        handleInputChange() {
            const input = this.$input[0];
            const value = this.$input.val();
            const length = value.length;

            // Update character count
            this.$characterCount.text(length);

            // Auto-resize textarea
            input.style.height = 'auto';
            input.style.height = Math.min(input.scrollHeight, 100) + 'px';

            // Enable/disable send button
            this.$sendButton.prop('disabled', value.trim() === '');

            // Add warning class if approaching limit
            if (length > 450) {
                this.$characterCount.addClass('qi-limit-warning');
            } else {
                this.$characterCount.removeClass('qi-limit-warning');
            }
        }

        scrollToBottom(smooth = true) {
            if (this.$messages[0]) {
                this.$messages[0].scrollTo({
                    top: this.$messages[0].scrollHeight,
                    behavior: smooth ? 'smooth' : 'auto'
                });
            }
        }

        createMessageElement(template) {
            return $(template).hide();
        }

        // Enhanced function to format markdown to HTML with improved features
        formatMarkdown(text) {
            if (!text) return '';

            try {
                // Validate input
                if (typeof text !== 'string') {
                    console.warn('formatMarkdown received non-string input:', typeof text);
                    return '';
                }

                // First sanitize the content to prevent XSS
                text = this.sanitizeInput(text);

                // Store code blocks temporarily to prevent interference
                const codeBlocks = [];
                let codeBlockIndex = 0;

                // Enhanced code block handling with language detection
                // More flexible regex to handle various code block formats
                text = text.replace(
                    /```([a-zA-Z0-9+\-#]*)?[\s]*\n?([\s\S]*?)\n?```/gm,
                    (match, language, code) => {
                        let lang = language ? language.toLowerCase() : 'text';

                        // Auto-detect language if not specified
                        if (!language && code.trim()) {
                            lang = this.detectCodeLanguage(code.trim());
                        }

                        // Clean and escape the code content
                        const cleanCode = code.trim();
                        const escapedCode = this.escapeHtml(cleanCode);
                        const placeholder = `__CODE_BLOCK_${codeBlockIndex}__`;
                        const displayLang = this.getLanguageDisplayName(lang);

                        // Check if code is long enough to need expand/collapse
                        const lineCount = cleanCode.split('\n').length;
                        const needsExpand = lineCount > 15;
                        const expandBtn = needsExpand ?
                          `<button class="qkb-code-expand-btn" title="Expand code">
                            <i class="fas fa-expand-alt"></i>
                          </button>` : '';

                        codeBlocks[codeBlockIndex] = `<div class="qkb-code-block${needsExpand ? ' qkb-code-collapsible' : ''}">
                          <div class="qkb-code-header">
                            <span class="qkb-code-language">${displayLang}</span>
                            <div class="qkb-code-actions">
                              <button class="qkb-code-action-btn qkb-line-numbers-btn" title="Toggle line numbers">
                                <i class="fas fa-list-ol"></i>
                              </button>
                              <button class="qkb-copy-code-btn" title="Copy code" data-code="${this.escapeHtml(cleanCode)}">
                                <i class="fas fa-copy"></i>
                              </button>
                            </div>
                          </div>
                          <pre><code class="language-${lang}">${this.addLineNumbers(escapedCode)}</code></pre>
                          ${expandBtn}
                        </div>`;
                        codeBlockIndex++;
                        return placeholder;
                    }
                );

                // Handle inline code
                text = text.replace(/`([^`]+)`/g, '<code class="qkb-inline-code">$1</code>');

                // Enhanced text formatting
                text = text.replace(/\*\*\*([^*]+)\*\*\*/g, '<strong><em>$1</em></strong>'); // Bold italic
                text = text.replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>'); // Bold
                text = text.replace(/\*([^*]+)\*/g, '<em>$1</em>'); // Italic
                text = text.replace(/~~([^~]+)~~/g, '<del>$1</del>'); // Strikethrough

                // Handle links with improved validation
                text = text.replace(/\[([^\]]+)\]\(([^)]+)\)/g, (match, linkText, url) => {
                    const sanitizedUrl = this.sanitizeUrl(url);
                    const sanitizedText = this.escapeHtml(linkText);
                    return sanitizedUrl ?
                        `<a href="${sanitizedUrl}" target="_blank" rel="noopener noreferrer">${sanitizedText}</a>` :
                        sanitizedText;
                });

                // Handle headings with classes
                text = text.replace(/^\s*####\s+(.+)$/gm, '<h4 class="qkb-message-heading">$1</h4>');
                text = text.replace(/^\s*###\s+(.+)$/gm, '<h3 class="qkb-message-heading">$1</h3>');
                text = text.replace(/^\s*##\s+(.+)$/gm, '<h2 class="qkb-message-heading">$1</h2>');
                text = text.replace(/^\s*#\s+(.+)$/gm, '<h1 class="qkb-message-heading">$1</h1>');

                // Enhanced list handling
                text = this.parseEnhancedLists(text);

                // Handle blockquotes
                text = text.replace(/^>\s+(.+)$/gm, '<blockquote class="qkb-message-quote">$1</blockquote>');

                // Enhanced paragraph and line break processing
                text = this.processContentBlocks(text);

                // Restore code blocks
                codeBlocks.forEach((codeBlock, index) => {
                    text = text.replace(`__CODE_BLOCK_${index}__`, codeBlock);
                });

                // Final cleanup
                text = this.finalizeContent(text);

                return text;
            } catch (error) {
                console.error('Error formatting markdown:', error);
                return `<p>${this.escapeHtml(text)}</p>`;
            }
        }

        // Helper functions for enhanced formatting
        detectCodeLanguage(code) {
            const patterns = {
                javascript: [/function\s+\w+/, /const\s+\w+\s*=/, /let\s+\w+\s*=/, /var\s+\w+\s*=/, /=>\s*{/, /console\.log/],
                python: [/def\s+\w+/, /import\s+\w+/, /from\s+\w+\s+import/, /print\s*\(/, /if\s+__name__\s*==/, /class\s+\w+:/],
                php: [/<\?php/, /\$\w+\s*=/, /function\s+\w+/, /echo\s+/, /->/, /namespace\s+/],
                java: [/public\s+class/, /public\s+static\s+void\s+main/, /System\.out\.println/, /import\s+java\./, /private\s+\w+/, /public\s+\w+/],
                css: [/\w+\s*{/, /:\s*\w+;/, /@media/, /\.[\w-]+\s*{/, /#[\w-]+\s*{/],
                html: [/<html/, /<div/, /<span/, /<p>/, /<h[1-6]>/, /<!DOCTYPE/],
                sql: [/SELECT\s+/, /FROM\s+/, /WHERE\s+/, /INSERT\s+INTO/, /UPDATE\s+/, /DELETE\s+FROM/],
                json: [/^\s*{/, /^\s*\[/, /"[\w-]+"\s*:/, /}\s*,?\s*$/],
                xml: [/<\?xml/, /<\/\w+>/, /<\w+[^>]*>/],
                bash: [/^#!/, /echo\s+/, /if\s*\[/, /for\s+\w+\s+in/, /grep\s+/, /awk\s+/],
                yaml: [/^\s*\w+:\s*$/, /^\s*-\s+/, /---/, /\.\.\./]
            };

            for (const [lang, regexes] of Object.entries(patterns)) {
                if (regexes.some(regex => regex.test(code))) {
                    return lang;
                }
            }

            return 'text';
        }

        getLanguageDisplayName(lang) {
            const displayNames = {
                javascript: 'JavaScript',
                python: 'Python',
                php: 'PHP',
                java: 'Java',
                css: 'CSS',
                html: 'HTML',
                sql: 'SQL',
                json: 'JSON',
                xml: 'XML',
                bash: 'Bash',
                yaml: 'YAML',
                text: 'Text'
            };

            return displayNames[lang] || lang.charAt(0).toUpperCase() + lang.slice(1);
        }

        /**
         * Add line numbers to code content
         */
        addLineNumbers(code) {
            try {
                const lines = code.split('\n');
                return lines.map(line => `<span class="line">${line}</span>`).join('\n');
            } catch (error) {
                console.error('Error adding line numbers:', error);
                return code;
            }
        }

        parseEnhancedLists(text) {
            // Handle unordered lists
            text = text.replace(/^\s*[-*+]\s+(.+)$/gm, '<li class="qkb-list-item">$1</li>');
            text = text.replace(/(<li class="qkb-list-item">.+<\/li>\s*)+/g, '<ul class="qkb-message-list">$&</ul>');

            // Handle ordered lists
            text = text.replace(/^\s*\d+\.\s+(.+)$/gm, '<li class="qkb-list-item">$1</li>');
            text = text.replace(/(<li class="qkb-list-item">.+<\/li>\s*)+/g, '<ol class="qkb-message-list qkb-ordered-list">$&</ol>');

            return text;
        }

        processContentBlocks(text) {
            try {
                // Split content into blocks separated by double line breaks
                const blocks = text.split(/\n\s*\n/);
                const processedBlocks = [];

                blocks.forEach(block => {
                    block = block.trim();
                    if (!block) return;

                    // Check if block is already a formatted element
                    if (block.match(/^<(h[1-6]|ul|ol|blockquote|div|table|pre)/)) {
                        processedBlocks.push(block);
                    } else {
                        // Handle single line breaks more carefully
                        const lines = block.split('\n');

                        if (lines.length === 1) {
                            // Single line - wrap in paragraph
                            processedBlocks.push(`<p class="qkb-message-paragraph">${block}</p>`);
                        } else {
                            // Multiple lines - check if they should be separate paragraphs or line breaks
                            const shouldBeParagraphs = lines.some(line =>
                                line.trim().length > 50 || // Long lines are likely separate thoughts
                                line.trim().match(/^[A-Z]/) // Lines starting with capital letters
                            );

                            if (shouldBeParagraphs) {
                                // Treat as separate paragraphs
                                lines.forEach(line => {
                                    line = line.trim();
                                    if (line) {
                                        processedBlocks.push(`<p class="qkb-message-paragraph">${line}</p>`);
                                    }
                                });
                            } else {
                                // Treat as line breaks within a paragraph
                                const formattedBlock = block.replace(/\n/g, '<br>');
                                processedBlocks.push(`<p class="qkb-message-paragraph">${formattedBlock}</p>`);
                            }
                        }
                    }
                });

                const result = processedBlocks.join('\n');

                // Validate the final HTML before returning
                if (typeof result !== 'string' || result.includes('<script') || result.includes('javascript:')) {
                    console.warn('Potentially unsafe HTML detected, falling back to escaped text');
                    return this.escapeHtml(arguments[0] || '');
                }

                return result;
            } catch (error) {
                console.error('Error processing content blocks:', error);
                return this.escapeHtml(arguments[0] || '');
            }
        }

        finalizeContent(text) {
            try {
                // Clean up empty paragraphs
                text = text.replace(/<p class="qkb-message-paragraph">\s*<\/p>/g, '');
                text = text.replace(/<p>\s*<\/p>/g, '');

                // Fix any double <br> tags that might have been created
                text = text.replace(/(<br>\s*){2,}/g, '<br><br>');

                // Clean up <br> tags that appear right after opening paragraph tags or before closing ones
                text = text.replace(/<p([^>]*)>\s*<br>/g, '<p$1>');
                text = text.replace(/<br>\s*<\/p>/g, '</p>');

                return text;
            } catch (error) {
                console.error('Error finalizing content:', error);
                return text;
            }
        }

        sanitizeInput(input) {
            if (typeof input !== 'string') return '';

            // For chat responses, we need to be careful not to over-sanitize
            // Remove only the most dangerous content while preserving markdown

            // Remove script tags and their content
            let sanitized = input.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');

            // Remove iframe tags
            sanitized = sanitized.replace(/<iframe\b[^>]*>.*?<\/iframe>/gi, '');

            // Remove dangerous event handlers
            sanitized = sanitized.replace(/\s+on\w+\s*=\s*["'][^"']*["']/gi, '');

            // Remove javascript: and data: URLs
            sanitized = sanitized.replace(/javascript:/gi, '');
            sanitized = sanitized.replace(/data:/gi, '');

            return sanitized;
        }

        escapeHtml(text) {
            if (typeof text !== 'string') return '';

            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        /**
         * Detect programming language from code content
         */
        detectCodeLanguage(code) {
            const patterns = {
                'javascript': [/function\s+\w+/, /const\s+\w+\s*=/, /let\s+\w+\s*=/, /var\s+\w+\s*=/, /=>\s*{/, /console\.log/, /document\./, /window\./],
                'php': [/<\?php/, /\$\w+/, /function\s+\w+\s*\(/, /class\s+\w+/, /namespace\s+/, /use\s+/],
                'python': [/def\s+\w+\s*\(/, /import\s+\w+/, /from\s+\w+\s+import/, /class\s+\w+\s*:/, /if\s+__name__\s*==/, /print\s*\(/],
                'css': [/\.\w+\s*{/, /#\w+\s*{/, /@media/, /background-color:/, /font-family:/, /margin:/],
                'html': [/<html/, /<head/, /<body/, /<div/, /<span/, /<p>/],
                'sql': [/SELECT\s+/, /FROM\s+/, /WHERE\s+/, /INSERT\s+INTO/, /UPDATE\s+/, /DELETE\s+FROM/],
                'json': [/^\s*{/, /^\s*\[/, /"[\w-]+"\s*:/, /}\s*,\s*{/],
                'bash': [/#!\/bin/, /echo\s+/, /grep\s+/, /awk\s+/, /sed\s+/, /\$\w+/],
                'xml': [/<\?xml/, /<\/\w+>/, /xmlns=/, /<\w+\s+\w+=/]
            };

            for (const [lang, regexes] of Object.entries(patterns)) {
                if (regexes.some(regex => regex.test(code))) {
                    return lang;
                }
            }

            return 'text';
        }

        /**
         * Get display name for programming language
         */
        getLanguageDisplayName(lang) {
            const displayNames = {
                'javascript': 'JavaScript',
                'js': 'JavaScript',
                'php': 'PHP',
                'python': 'Python',
                'py': 'Python',
                'css': 'CSS',
                'html': 'HTML',
                'sql': 'SQL',
                'json': 'JSON',
                'bash': 'Bash',
                'sh': 'Shell',
                'xml': 'XML',
                'yaml': 'YAML',
                'yml': 'YAML',
                'markdown': 'Markdown',
                'md': 'Markdown',
                'text': 'Text',
                'txt': 'Text'
            };

            return displayNames[lang] || lang.toUpperCase();
        }

        /**
         * Apply syntax highlighting to code blocks
         */
        applySyntaxHighlighting($element) {
            try {
                // Check if Prism is available
                if (typeof window.Prism !== 'undefined') {
                    // Find all code blocks and apply highlighting
                    $element.find('code[class*="language-"]').each(function() {
                        const $code = $(this);
                        const codeElement = $code[0];

                        // Apply Prism highlighting
                        window.Prism.highlightElement(codeElement);
                    });
                } else {
                    // Fallback: add a class for basic styling
                    $element.find('code[class*="language-"]').addClass('qkb-code-fallback');
                }
            } catch (error) {
                console.error('Error applying syntax highlighting:', error);
            }
        }

        /**
         * Add enhanced functionality to code blocks
         */
        addCopyCodeButtons($element) {
            try {
                // Copy button functionality
                $element.find('.qkb-copy-code-btn').off('click').on('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();

                    const $button = $(e.currentTarget);
                    const $codeBlock = $button.closest('.qkb-code-block');
                    const $code = $codeBlock.find('code');

                    // Get the code text
                    let codeText = $button.data('code') || $code.text();

                    // Copy to clipboard
                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(codeText).then(() => {
                            this.showCopySuccess($button);
                        }).catch(() => {
                            this.fallbackCopyToClipboard(codeText, $button);
                        });
                    } else {
                        this.fallbackCopyToClipboard(codeText, $button);
                    }
                });

                // Line numbers toggle
                $element.find('.qkb-line-numbers-btn').off('click').on('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    const $codeBlock = $(e.target).closest('.qkb-code-block');
                    $codeBlock.toggleClass('qkb-show-line-numbers');

                    const $button = $(e.target).closest('.qkb-line-numbers-btn');
                    const isShowing = $codeBlock.hasClass('qkb-show-line-numbers');
                    $button.attr('title', isShowing ? 'Hide line numbers' : 'Show line numbers');
                    $button.find('i').toggleClass('fas fa-list-ol fas fa-list');
                });

                // Code expand/collapse
                $element.find('.qkb-code-expand-btn').off('click').on('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    const $codeBlock = $(e.target).closest('.qkb-code-block');
                    const $button = $(e.target).closest('.qkb-code-expand-btn');

                    $codeBlock.toggleClass('qkb-code-expanded');
                    const isExpanded = $codeBlock.hasClass('qkb-code-expanded');

                    $button.html(isExpanded ?
                        '<i class="fas fa-compress-alt"></i>' :
                        '<i class="fas fa-expand-alt"></i>'
                    );
                    $button.attr('title', isExpanded ? 'Collapse code' : 'Expand code');
                });
            } catch (error) {
                console.error('Error adding code block functionality:', error);
            }
        }

        /**
         * Show copy success feedback
         */
        showCopySuccess($button) {
            const originalIcon = $button.find('i').attr('class');
            $button.find('i').attr('class', 'fas fa-check');
            $button.attr('title', 'Copied!');

            setTimeout(() => {
                $button.find('i').attr('class', originalIcon);
                $button.attr('title', 'Copy code');
            }, 2000);
        }

        /**
         * Fallback copy to clipboard method
         */
        fallbackCopyToClipboard(text, $button) {
            try {
                const textArea = document.createElement('textarea');
                textArea.value = text;
                textArea.style.position = 'fixed';
                textArea.style.left = '-999999px';
                textArea.style.top = '-999999px';
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();

                const successful = document.execCommand('copy');
                document.body.removeChild(textArea);

                if (successful) {
                    this.showCopySuccess($button);
                }
            } catch (error) {
                console.error('Fallback copy failed:', error);
            }
        }

        sanitizeUrl(url) {
            if (typeof url !== 'string') return null;

            try {
                // Remove dangerous protocols
                if (/^(javascript|data|vbscript):/i.test(url)) {
                    return null;
                }

                // Ensure URL starts with http or https
                if (!/^https?:\/\//i.test(url)) {
                    url = 'https://' + url;
                }

                // Validate URL format
                const urlObj = new URL(url);
                return urlObj.href;
            } catch (e) {
                return null;
            }
        }

        async addMessage(message, isUser, type = 'message', animate = true, isHtml = false, isRichText = false) {
            try {
                const timestamp = Date.now();
                let formattedMessage;

                if (!isUser && type === 'message') {
                    if (isRichText) {
                        // For rich text content from quick access buttons
                        // This may include processed shortcodes from WordPress
                        formattedMessage = message;
                    } else if (isHtml) {
                        formattedMessage = message;
                    } else {
                        formattedMessage = this.formatMarkdown(message);
                    }
                } else {
                    formattedMessage = message;
                }

                const $messageElement = $(this.templates[isUser ? 'userMessage' : 'botMessage']);
                const $content = $messageElement.find('.qkb-message-content');

                if (type === 'error') {
                    $content.html(`
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="10"></circle>
                            <line x1="12" y1="8" x2="12" y2="12"></line>
                            <line x1="12" y1="16" x2="12.01" y2="16"></line>
                        </svg>
                        <span class="qkb-error-text">${message}</span>
                    `);
                } else {
                    // Set the content with error handling
                    try {
                        if (formattedMessage && typeof formattedMessage === 'string') {
                            $content.html(formattedMessage);
                        } else {
                            $content.text(message);
                        }
                    } catch (error) {
                        console.error('Error setting message content:', error);
                        $content.text(message);
                    }

                    // Check for quick action patterns and assistant action patterns in the message
                    if (!isUser) {
                        this.checkForQuickActionPatterns($content);
                        this.checkForAssistantActionPatterns($content);
                    }
                }

                // Add timestamp data and formatted time display
                $messageElement
                    .attr('data-timestamp', timestamp)
                    .append(`<div class="qkb-message-time">${new Date(timestamp).toLocaleTimeString()}</div>`);

                // If not animating, show immediately
                if (!animate) {
                    $messageElement.show();
                }

                // Insert message in correct chronological position
                let inserted = false;
                const $existingMessages = this.$messages.find('.qkb-message');

                $existingMessages.each((_, el) => {
                    const elTimestamp = parseInt($(el).attr('data-timestamp')) || 0;
                    if (timestamp < elTimestamp) {
                        $(el).before($messageElement);
                        inserted = true;
                        return false;
                    }
                });

                if (!inserted) {
                    this.$messages.append($messageElement);
                }

                if (animate) {
                    $messageElement.hide().fadeIn(300, () => {
                        this.scrollToBottom();
                    });
                }

                if (!isUser && type === 'message') {
                    const messageId = 'msg_' + timestamp;
                    $messageElement
                        .attr('data-message-id', messageId)
                        .attr('data-query', this.lastQuery || '')
                        .attr('data-response', message);

                    if (animate) {
                        setTimeout(() => {
                            $messageElement.find('.qkb-message-feedback').addClass('visible');
                        }, 1000);
                    } else {
                        $messageElement.find('.qkb-message-feedback').addClass('visible');
                    }

                    // Process any quick action triggers in the message
                    this.processQuickActionTriggers($content);
                }

                // Add ARIA attributes
                $messageElement.attr({
                    'role': 'listitem',
                    'aria-label': `${isUser ? 'You' : 'Ask Q'}: ${message}`,
                    'tabindex': '0'
                });

                // Apply syntax highlighting and add copy functionality
                if (!isUser) {
                    this.applySyntaxHighlighting($messageElement);
                    this.addCopyCodeButtons($messageElement);
                }

                return $messageElement;
            } catch (error) {
                console.error('Error adding message:', error);
            }
        }

        async sendMessage() {
            const message = this.$input.val().trim();

            if (!message || message.length > this.maxMessageLength) {
                this.showError(qkbChatbot?.strings?.char_limit_exceeded || 'Message exceeds character limit');
                return;
            }

            try {
                this.lastQuery = message;
                $('.qkb-welcome-screen').remove();

                this.$input.prop('disabled', true);
                this.$sendButton.prop('disabled', true);

                const timestamp = Date.now();

                // Display message
                let displayMessage = message;

                // Store user message with assistant ID
                const userMessageData = {
                    message: displayMessage,
                    isUser: true,
                    timestamp: timestamp,
                    assistantId: this.currentAssistant
                };

                let chatHistory = JSON.parse(localStorage.getItem('qkb_chat_history')) || [];
                chatHistory.push(userMessageData);
                localStorage.setItem('qkb_chat_history', JSON.stringify(chatHistory));

                // Display user message
                await this.addMessage(displayMessage, true);

                this.showLoadingIndicator();
                this.$input.val('');
                this.$input.trigger('input');

                // Create form data for file upload if needed
                const formData = new FormData();
                formData.append('action', 'qkb_chat_request');
                formData.append('nonce', qkbChatbot.nonce);
                formData.append('message', message);
                formData.append('is_default_assistant', this.isUsingDefaultAssistant);
                formData.append('assistant_id', this.currentAssistant);

                // File attachment functionality has been removed

                const response = await $.ajax({
                    url: qkbChatbot.ajax_url,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false
                });

                this.hideLoadingIndicator();

                if (response.success && response.data) {
                    const botMessage = response.data.message;
                    const botTimestamp = Date.now();

                    // Store bot message with assistant ID
                    const botMessageData = {
                        message: botMessage,
                        isUser: false,
                        timestamp: botTimestamp,
                        assistantId: this.currentAssistant
                    };

                    chatHistory = JSON.parse(localStorage.getItem('qkb_chat_history')) || [];
                    chatHistory.push(botMessageData);
                    localStorage.setItem('qkb_chat_history', JSON.stringify(chatHistory));

                    // Display bot message
                    await this.addMessage(botMessage, false);
                }

            } catch (error) {
                console.error('Chat Error:', error);
                await this.addMessage(
                    'I apologize, but I encountered an error processing your request. Please try again.',
                    false,
                    'error'
                );
            } finally {
                this.$input.prop('disabled', false);
                this.$sendButton.prop('disabled', false);
                this.$input.focus();
            }
        }

        loadChatHistory() {
            try {
                // Get stored chat history
                const chatHistory = JSON.parse(localStorage.getItem('qkb_chat_history')) || [];

                // If there's chat history, add messages without animation
                if (chatHistory.length > 0) {
                    this.$messages.addClass('has-chat-history');

                    chatHistory.forEach(async (item) => {
                        if (item.assistantId === this.currentAssistant) {
                            await this.addMessage(
                                item.message,
                                item.isUser,
                                'message',
                                false // Don't animate historical messages
                            );
                        }
                    });

                    // Scroll to bottom after loading history
                    this.scrollToBottom(false);
                } else {
                    // If no history, prepare for splash/welcome screen
                    this.$messages.removeClass('has-chat-history');
                }

            } catch (error) {
                console.error('Error loading chat history:', error);
                this.$messages.removeClass('has-chat-history');
            }
        }

        setupFeedbackHandlers() {
            // Remove any existing feedback handlers to prevent duplicates
            $(document).off('click.qkbFeedback', '.qkb-feedback-btn');

            // Set up feedback handlers with namespace to prevent duplicates
            $(document).on('click.qkbFeedback', '.qkb-feedback-btn', async (e) => {
                e.preventDefault();
                e.stopPropagation();

                const $button = $(e.currentTarget);
                const $message = $button.closest('.qkb-message');

                // Prevent multiple submissions - check multiple conditions
                if ($message.hasClass('feedback-submitted') || $button.hasClass('submitting') || $button.prop('disabled')) {
                    console.log('Feedback submission prevented - already submitted or in progress');
                    return;
                }

                // Mark as submitting to prevent duplicate submissions
                $button.addClass('submitting').prop('disabled', true);
                $message.find('.qkb-feedback-btn').prop('disabled', true);

                const messageId = $message.attr('data-message-id');
                const feedback = $button.data('value') === 'helpful' ? 1 : -1;

                // Store query and response for reference
                this.lastQuery = $message.data('query') || '';
                this.lastResponse = $message.data('response') || '';

                console.log('Submitting feedback:', {
                    messageId: messageId,
                    feedback: feedback,
                    query: this.lastQuery,
                    response: this.lastResponse
                });

                try {
                    await this.submitFeedback(messageId, feedback);

                    // Show success feedback
                    $message.addClass('feedback-submitted');
                    $button.addClass('selected');
                    $message.find('.qkb-feedback-prompt')
                        .html('<span class="qkb-feedback-thanks">Thanks for your feedback!</span>')
                        .fadeOut(3000);

                } catch (error) {
                    console.error('Failed to submit feedback:', error);
                    // Show error message to user
                    $message.find('.qkb-feedback-prompt')
                        .html('<span class="qkb-feedback-error">Failed to submit feedback. Please try again.</span>')
                        .fadeOut(3000);

                    // Re-enable buttons on error
                    $button.removeClass('submitting').prop('disabled', false);
                    $message.find('.qkb-feedback-btn').prop('disabled', false);
                }
            });
        }

        initializeInputEnhancements() {
            // We're now using the built-in character counter
            this.$input.on('input', () => {
                const length = this.$input.val().length;

                // Update send button state based on input length
                const isValid = length > 0 && length <= this.maxMessageLength;
                this.$sendButton.prop('disabled', !isValid);

                if (length >= this.maxMessageLength) {
                    this.$characterCount.addClass('qi-limit-reached');
                } else {
                    this.$characterCount.removeClass('qi-limit-reached');
                }

                if (length > 0) {
                    this.handleTypingIndicator();
                }
            });


        }



        /**
         * Handle file selection - removed
         */

        handleTypingIndicator() {
            clearTimeout(this.typingTimeout);
            this.isTyping = true;

            this.typingTimeout = setTimeout(() => {
                this.isTyping = false;
            }, 1000);
        }

        /**
         * Check for quick action patterns in the message content
         * @param {jQuery} $content - The message content element
         */
        checkForQuickActionPatterns($content) {
            // Look for {quick_action:NAME} pattern in the text
            const text = $content.text();
            const regex = /\{quick_action:(.*?)\}/g;
            let match;
            let found = false;

            // Find all matches
            while ((match = regex.exec(text)) !== null) {
                found = true;
                const actionName = match[1].trim();

                // Find the matching quick access button by name
                const $quickAccessButtons = $('.qkb-quick-access-button');
                let $matchingButton = null;

                $quickAccessButtons.each((_, button) => {
                    const $button = $(button);
                    const buttonName = $button.find('span').text();
                    if (buttonName.toLowerCase() === actionName.toLowerCase()) {
                        $matchingButton = $button;
                        return false; // Break the loop
                    }
                });

                if ($matchingButton) {
                    // Get the content ID and other data
                    const contentId = $matchingButton.data('content-id');
                    const showInModal = $matchingButton.data('show-modal') === 1;
                    const modalTitle = $matchingButton.data('modal-title') || actionName;

                    // Get the content from the hidden div
                    const content = $('#' + contentId).html();

                    if (content) {
                        if (showInModal) {
                            // For modal content, replace the pattern with a message indicating the modal is open
                            const modalMessage = `<em>Opening "${modalTitle}" in a modal window...</em>`;
                            const htmlContent = $content.html().replace(
                                new RegExp(`\\{quick_action:${actionName}\\}`, 'g'),
                                modalMessage
                            );
                            $content.html(htmlContent);

                            // Show content in modal
                            const $modal = $('.qkb-quick-access-modal');

                            $modal.find('.qkb-modal-title').text(modalTitle);
                            $modal.find('.qkb-modal-content').html(content);
                            $modal.addClass('active');

                            // Add event listener for close button if not already added
                            if (!this.modalEventsInitialized) {
                                $(document).on('click', '.qkb-quick-access-modal .qkb-modal-close, .qkb-quick-access-modal .qkb-modal-overlay', () => {
                                    $('.qkb-quick-access-modal').removeClass('active');
                                });

                                // Prevent clicks on modal content from closing the modal
                                $(document).on('click', '.qkb-quick-access-modal .qkb-modal-content', (e) => {
                                    e.stopPropagation();
                                });

                                this.modalEventsInitialized = true;
                            }

                            // Initialize Formidable Forms handler for the modal
                            this.initFormidableFormsHandler($('.qkb-quick-access-modal'));
                        } else {
                            // For regular content, replace the pattern with the actual content
                            const htmlContent = $content.html().replace(
                                new RegExp(`\\{quick_action:${actionName}\\}`, 'g'),
                                content
                            );
                            $content.html(htmlContent);
                        }
                    }
                }
            }

            return found;
        }

        /**
         * Check for assistant action patterns in the message content
         * @param {jQuery} $content - The message content element
         */
        checkForAssistantActionPatterns($content) {
            // Look for {assistant_action:NAME} pattern in the text
            const text = $content.text();
            const regex = /\{assistant_action:(.*?)\}/g;
            let match;
            let found = false;

            // Get the current assistant ID
            const assistantId = this.currentAssistant;

            // If no assistant ID, return
            if (!assistantId) {
                return false;
            }

            // Get assistant actions from the global object
            const assistantActions = (typeof qkbChatbot !== 'undefined' && qkbChatbot?.assistant_actions && qkbChatbot.assistant_actions[assistantId]) || [];

            // Debug log for assistant actions
            console.log('Assistant Actions for ID ' + assistantId + ':', assistantActions);

            // Find all matches
            while ((match = regex.exec(text)) !== null) {
                found = true;
                const actionName = match[1].trim();

                // Find the matching assistant action by name
                const matchingAction = assistantActions.find(action =>
                    action.name.toLowerCase() === actionName.toLowerCase()
                );

                if (matchingAction) {
                    // Get the content and other data
                    // The content already has shortcodes processed by the server
                    const content = matchingAction.content;
                    const showInModal = matchingAction.show_in_modal === true || matchingAction.show_in_modal === 1;
                    const modalTitle = matchingAction.modal_title || actionName;

                    // Debug log for matched action
                    console.log('Matched Assistant Action:', matchingAction);
                    console.log('Action Content:', content);

                    if (content) {
                        if (showInModal) {
                            // For modal content, replace the pattern with a message indicating the modal is open
                            const modalMessage = `<em>Opening "${modalTitle}" in a modal window...</em>`;
                            const htmlContent = $content.html().replace(
                                new RegExp(`\\{assistant_action:${actionName}\\}`, 'g'),
                                modalMessage
                            );
                            $content.html(htmlContent);

                            // Show content in modal
                            const $modal = $('.qkb-quick-access-modal');

                            $modal.find('.qkb-modal-title').text(modalTitle);

                            // We need to create a temporary div to properly handle HTML content
                            const $tempDiv = $('<div></div>').html(content);
                            const processedContent = $tempDiv.html();

                            $modal.find('.qkb-modal-content').html(processedContent);
                            $modal.addClass('active');

                            // Add event listener for close button if not already added
                            if (!this.modalEventsInitialized) {
                                $(document).on('click', '.qkb-quick-access-modal .qkb-modal-close, .qkb-quick-access-modal .qkb-modal-overlay', () => {
                                    $('.qkb-quick-access-modal').removeClass('active');
                                });

                                // Prevent clicks on modal content from closing the modal
                                $(document).on('click', '.qkb-quick-access-modal .qkb-modal-content', (e) => {
                                    e.stopPropagation();
                                });

                                this.modalEventsInitialized = true;
                            }

                            // Initialize Formidable Forms handler for the modal
                            this.initFormidableFormsHandler($('.qkb-quick-access-modal'));
                        } else {
                            // For regular content, replace the pattern with the actual content
                            // We need to create a temporary div to properly handle HTML content
                            const $tempDiv = $('<div></div>').html(content);
                            const processedContent = $tempDiv.html();

                            const htmlContent = $content.html().replace(
                                new RegExp(`\\{assistant_action:${actionName}\\}`, 'g'),
                                processedContent
                            );
                            $content.html(htmlContent);

                            // Initialize Formidable Forms in the content
                            this.initFormidableFormsHandler($content);
                        }
                    }
                }
            }

            return found;
        }

        /**
         * Process quick action triggers in a message
         * @param {jQuery} $content - The message content element
         */
        processQuickActionTriggers($content) {
            // Set up event handlers for trigger buttons
            $content.find('.qkb-quick-action-trigger-button').on('click', (e) => {
                e.preventDefault();
                const $button = $(e.currentTarget);
                const actionName = $button.data('action-name');
                const contentId = $button.data('content-id');
                const showInModal = $button.data('show-modal') === 1;
                const modalTitle = $button.data('modal-title');

                // Get the content from the hidden div
                const content = $('#' + contentId).html();

                if (content) {
                    if (showInModal) {
                        // Show content in modal
                        const $modal = $('.qkb-quick-access-modal');

                        $modal.find('.qkb-modal-title').text(modalTitle || actionName);
                        $modal.find('.qkb-modal-content').html(content);
                        $modal.addClass('active');

                        // Add event listener for close button if not already added
                        if (!this.modalEventsInitialized) {
                            $(document).on('click', '.qkb-quick-access-modal .qkb-modal-close, .qkb-quick-access-modal .qkb-modal-overlay', () => {
                                $('.qkb-quick-access-modal').removeClass('active');
                            });

                            // Prevent clicks on modal content from closing the modal
                            $(document).on('click', '.qkb-quick-access-modal .qkb-modal-content', (e) => {
                                e.stopPropagation();
                            });

                            // Initialize Formidable Forms submission handler
                            this.initFormidableFormsHandler($modal);

                            this.modalEventsInitialized = true;
                        } else {
                            // Initialize Formidable Forms submission handler even if modal events are already initialized
                            this.initFormidableFormsHandler($modal);
                        }
                    } else {
                        // Add the content as a bot message with rich text formatting
                        this.addMessage(content, false, 'message', true, false, true);
                    }
                }
            });
        }

        /**
         * Initialize Formidable Forms submission handler
         * @param {jQuery} $modal - The modal element containing the form
         */
        initFormidableFormsHandler($modal) {
            // Find any Formidable Forms within the modal - use a more general selector to catch all forms
            const $formidableForms = $modal.find('form');

            console.log('Found forms in modal:', $formidableForms.length);

            if ($formidableForms.length > 0) {
                // For each form in the modal
                $formidableForms.each(function() {
                    const $form = $(this);
                    console.log('Processing form:', $form.attr('id'));

                    // Skip if already processed by our handler
                    if ($form.data('qkb-processed')) {
                        console.log('Form already processed by our handler, skipping');
                        return;
                    }

                    // Mark as processed to avoid duplicate handlers
                    $form.data('qkb-processed', true);

                    // Add a hidden input to indicate this is from an assistant action
                    if (!$form.find('input[name="qkb_assistant_action"]').length) {
                        $form.append('<input type="hidden" name="qkb_assistant_action" value="1">');
                    }

                    // Add a hidden input for AJAX flag
                    if (!$form.find('input[name="frm_ajax"]').length) {
                        $form.append('<input type="hidden" name="frm_ajax" value="1">');
                    }

                    // Let the formidable-forms-handler.js handle the form submission
                    // This is just a fallback in case that handler doesn't catch it

                    // Add a loading overlay to the form container
                    const $formContainer = $form.parent();
                    const $loadingOverlay = $('<div class="qkb-form-loading-overlay"><div class="qkb-form-spinner"></div><div class="qkb-form-loading-text">Submitting form...</div></div>');
                    $formContainer.css('position', 'relative');

                    // Add a success message container
                    const $successContainer = $('<div class="qkb-form-success-container" style="display:none;"></div>');
                    $formContainer.append($successContainer);

                    // Only add our handler if the form doesn't already have a submit handler
                    if (!$._data($form[0], 'events') || !$._data($form[0], 'events').submit) {
                        console.log('Adding fallback submit handler to form');

                        // Handle form submission
                        $form.on('submit', function(e) {
                            console.log('Form submit event triggered in chatbot.js handler');
                            e.preventDefault();

                            // Show loading overlay
                            $formContainer.append($loadingOverlay);

                            // Get form ID
                            let formId = $form.find('input[name="form_id"]').val();
                            if (!formId) {
                                const formIdMatch = $form.attr('id') ? $form.attr('id').match(/form_(\d+)/) : null;
                                if (formIdMatch) {
                                    formId = formIdMatch[1];
                                }
                            }

                            console.log('Form ID:', formId);

                            // Create FormData object
                            const formData = new FormData($form[0]);

                            // Add assistant action flag
                            formData.append('qkb_assistant_action', '1');
                            formData.append('frm_ajax', '1');

                            // Submit using AJAX
                            $.ajax({
                                url: qkbChatbot.ajax_url,
                                type: 'POST',
                                data: formData,
                                processData: false,
                                contentType: false,
                                success: function(response) {
                                    console.log('Form submission response:', response);

                                    // Remove loading overlay
                                    $loadingOverlay.remove();

                                    // Show success message
                                    $form.hide();
                                    $successContainer.html('<div class="qkb-form-success">Form submitted successfully!</div>' +
                                        '<button class="qkb-form-close-button">Close</button>').show();

                                    // Add close button handler
                                    $successContainer.find('.qkb-form-close-button').on('click', function() {
                                        $('.qkb-quick-access-modal').removeClass('active');
                                    });
                                },
                                error: function(xhr, status, error) {
                                    console.error('Form submission error:', error);

                                    // Remove loading overlay
                                    $loadingOverlay.remove();

                                    // Try direct form submission as fallback
                                    console.log('Trying direct form submission as fallback');
                                    $form.off('submit'); // Remove our handler
                                    $form.submit(); // Submit the form directly
                                }
                            });
                        });
                    }
                });
            }
        }


        showLoadingIndicator() {
            const $typingIndicator = $(`
                <div class="qkb-message qkb-bot-message qkb-typing-indicator"
                    role="status"
                    aria-label="Assistant is thinking">
                    <div class="qkb-typing-content">
                        <div class="qkb-typing-text">Thinking</div>
                        <div class="qkb-typing-dots">
                            <div class="qkb-typing-dot"></div>
                            <div class="qkb-typing-dot"></div>
                            <div class="qkb-typing-dot"></div>
                        </div>
                    </div>
                </div>
            `);
            this.$messages.append($typingIndicator);
            this.scrollToBottom();
        }

        hideLoadingIndicator() {
            $('.qkb-typing-indicator').remove();
        }

        async showWelcomeScreen() {
            // Use the welcomeScreen function instead of a static template
            const welcomeScreenHtml = this.templates.welcomeScreen.call(this);
            const $welcomeScreen = $(welcomeScreenHtml);
            this.$messages.html($welcomeScreen);

            // Add event listeners to suggestion buttons
            $('.qkb-suggestion-button').on('click', (e) => {
                const suggestionText = $(e.currentTarget).text();
                this.$input.val(suggestionText);
                this.$input.trigger('input');
                this.sendMessage();
            });

            // Try to load particles.js if not already loaded
            if (typeof particlesJS === 'undefined') {
                try {
                    // Load particles.js dynamically
                    const script = document.createElement('script');
                    script.src = 'https://cdn.jsdelivr.net/particles.js/2.0.0/particles.min.js';
                    document.head.appendChild(script);

                    // Wait for script to load
                    script.onload = () => {
                        this.initParticles();
                    };
                } catch (error) {
                    console.error('Failed to load particles.js:', error);
                }
            } else {
                // Initialize particles with a delay to ensure DOM is ready
                setTimeout(() => {
                    this.initParticles();
                }, 100);
            }

            this.scrollToBottom();
        }

        initParticles() {
            try {
                const particlesContainer = document.getElementById('qkb-particles-js');

                if (!particlesContainer) {
                    console.warn('Particles container not found');
                    return;
                }

                particlesJS('qkb-particles-js', {
                    particles: {
                        number: { value: 20, density: { enable: true, value_area: 800 } },
                        color: { value: '#2271b1' },
                        shape: { type: 'circle' },
                        opacity: { value: 0.6, random: true },
                        size: { value: 4, random: true },
                        line_linked: { enable: true, distance: 150, color: '#2271b1', opacity: 0.3, width: 1 },
                        move: { enable: true, speed: 2, direction: 'none', random: true, straight: false, out_mode: 'out' }
                    },
                    interactivity: {
                        detect_on: 'canvas',
                        events: {
                            onhover: { enable: true, mode: 'grab' },
                            onclick: { enable: true, mode: 'push' },
                            resize: true
                        },
                        modes: { grab: { distance: 140, line_linked: { opacity: 1 } } }
                    },
                    retina_detect: true
                });
                console.log('Particles initialized successfully');
            } catch (error) {
                console.error('Error initializing particles:', error);
            }
        }

        showSplashScreen() {
            // Check if there are any messages in the chat
            const hasMessages = this.$messages.find('.qkb-message').length > 0;

            // If there are messages, skip the splash screen
            if (hasMessages) {
                return;
            }

            // Use configured delay from localized settings or a default of 4500ms
            const splashDelay = (qkbChatbot.splashScreenConfig && qkbChatbot.splashScreenConfig.delay) || 4500;
            const $splash = $(this.templates.splashScreen);
            this.$messages.prepend($splash);

            // After the configured delay, add the fade-out class to trigger the CSS animation
            setTimeout(() => {
                $splash.addClass('fade-out');
                // Remove splash screen when the animation ends
                $splash.one('animationend', () => {
                    $splash.remove();
                    // Show welcome screen after splash if no messages
                    if (!this.$messages.find('.qkb-message').length) {
                        this.showWelcomeScreen();
                    }
                });
            }, splashDelay);
        }

        startNewChat() {
            // Show modal
            const $modal = $('.qkb-new-chat-modal');
            $modal.addClass('active');

            // Add backdrop effect
            $('.qkb-chatbot-messages').css('opacity', '0.5');

            // Update modal text to be more specific
            $('.qkb-new-chat-modal .qkb-modal-content').html(`
                <p>Are you sure you want to start a new chat? This will clear your conversation history with the current assistant.</p>
            `);

            // Handle confirm
            $('.qkb-confirm-new-chat').one('click', async () => {
                await this.clearChat();
                this.closeNewChatModal();
            });

            // Handle cancel
            $('.qkb-cancel-new-chat').one('click', () => {
                this.closeNewChatModal();
            });
        }

        closeNewChatModal() {
            const $modal = $('.qkb-new-chat-modal');
            $modal.removeClass('active');
            $('.qkb-chatbot-messages').css('opacity', '1');
        }

        async clearChat() {
            try {
                // Remove all messages with animation
                const $messages = this.$messages.find('.qkb-message');
                await Promise.all($messages.map(async (_, el) => {
                    return new Promise(resolve => {
                        $(el).fadeOut(300, function() {
                            $(this).remove();
                            resolve();
                        });
                    });
                }));

                // Get current chat history and filter out current assistant messages
                let chatHistory = JSON.parse(localStorage.getItem('qkb_chat_history')) || [];
                chatHistory = chatHistory.filter(item => item.assistantId !== this.currentAssistant);

                // Save filtered history back to localStorage
                localStorage.setItem('qkb_chat_history', JSON.stringify(chatHistory));

                // Reset states for current assistant
                this.messageHistory = [];
                this.interactionHistory = [];
                this.currentSuggestionIndex = -1;

                // Reset input
                this.$input.val('');
                this.$sendButton.prop('disabled', true);

                // Reset visual states
                this.$messages.removeClass('has-chat-history');

                // Show welcome screen with animation
                this.showWelcomeScreen(true);

                return true;
            } catch (error) {
                console.error('Error clearing chat:', error);
                return false;
            }
        }

        initializeKeyboardNavigation() {
            // Keyboard navigation functionality
            this.$container.on('keydown', (e) => {
                if (e.key === 'Tab') {
                    const focusableElements = this.$container.find(
                        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
                    ).filter(':visible');

                    const firstElement = focusableElements.first();
                    const lastElement = focusableElements.last();

                    if (e.shiftKey) { // Shift + Tab
                        if (document.activeElement === firstElement[0]) {
                            e.preventDefault();
                            lastElement.focus();
                        }
                    } else { // Tab
                        if (document.activeElement === lastElement[0]) {
                            e.preventDefault();
                            firstElement.focus();
                        }
                    }
                }
            });
        }

        initializeBasicAccessibility() {
            // Initialize only critical accessibility features
            this.$container.attr({
                'role': 'dialog',
                'aria-label': 'Chat with Ask Q',
                'aria-modal': 'true'
            });

            this.$messages.attr({
                'role': 'log',
                'aria-live': 'polite'
            });

            this.$input.attr({
                'role': 'textbox',
                'aria-label': 'Chat message'
            });

            // Basic button labels
            this.$sendButton.attr('aria-label', 'Send message');
        }

        appendMessage(messageContent, isUser) {
            const messageTemplate = isUser ? this.templates.userMessage : this.templates.botMessage;
            const $message = $(messageTemplate);

            $message.find('.qkb-message-content').html(messageContent);

            this.$messages.append($message);
            this.scrollToBottom();
        }

        async loadMLPatterns() {
            if (!qkbChatbot?.ajax_url || !qkbChatbot?.nonce) {
                console.warn('Required AJAX configuration is missing');
                this.mlPatterns = new Map();
                return false;
            }

            try {
                const response = await $.ajax({
                    url: qkbChatbot.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'qkb_get_ml_patterns',
                        nonce: qkbChatbot.nonce
                    },
                    dataType: 'json'
                });

                if (!response?.success) {
                    console.warn('ML patterns response was not successful:', response?.data);
                    this.mlPatterns = new Map();
                    return false;
                }

                // Safely handle the response data
                if (response?.data && typeof response.data === 'object') {
                    this.mlPatterns = new Map(
                        Object.entries(response.data).map(([key, value]) => {
                            // Ensure value is properly structured
                            const safeValue = {
                                response: value?.response || '',
                                success_rate: parseFloat(value?.success_rate) || 0,
                                usage_count: parseInt(value?.usage_count) || 0
                            };
                            return [key, safeValue];
                        })
                    );
                    return true;
                }

                // Fallback to empty patterns if data is invalid
                this.mlPatterns = new Map();
                return false;

            } catch (error) {
                console.warn('Error loading ML patterns:', error);
                this.mlPatterns = new Map();
                return false;
            }
        }

        getMLSuggestion(query) {
            if (!this.mlPatterns || this.mlPatterns.size === 0) {
                return null;
            }

            try {
                const normalizedQuery = query.toLowerCase().trim();
                let bestMatch = null;
                let highestScore = 0;

                for (const [pattern, data] of this.mlPatterns) {
                    const score = this.calculateSimilarity(normalizedQuery, pattern);
                    if (score > highestScore && score > 0.7) { // Minimum threshold
                        highestScore = score;
                        bestMatch = {
                            response: data.response,
                            confidence: score,
                            usage_count: data.usage_count
                        };
                    }
                }

                return bestMatch;

            } catch (error) {
                console.warn('Error getting ML suggestion:', error);
                return null;
            }
        }

        calculateSimilarity(text1, text2) {
            try {
                const words1 = text1.split(/\s+/);
                const words2 = text2.split(/\s+/);
                const intersection = words1.filter(word => words2.includes(word));
                const union = new Set([...words1, ...words2]);
                return intersection.length / union.size;
            } catch (error) {
                console.warn('Error calculating similarity:', error);
                return 0;
            }
        }

        handleResponse(response) {
            const messageType = response.type || 'direct_answer';

            switch(messageType) {
                case 'direct_answer':
                    this.appendMessage(response.message, 'bot');
                    if (response.related_articles.length > 0) {
                        this.showRelatedArticles(response.related_articles);
                    }
                    break;

                case 'resource_list':
                    this.showResourceCarousel(response.related_articles);
                    break;

                default:
                    this.appendMessage(response.message, 'bot');
            }

            if (response.sources.length > 0) {
                this.showSources(response.sources);
            }
        }

        trapFocus(modal) {
            const focusableElements = modal.find('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
            const firstFocusable = focusableElements[0];
            const lastFocusable = focusableElements[focusableElements.length - 1];

            firstFocusable.focus();

            modal.on('keydown', function(e) {
                if (e.key === 'Tab') {
                    if (e.shiftKey) {
                        if (document.activeElement === firstFocusable) {
                            e.preventDefault();
                            lastFocusable.focus();
                        }
                    } else {
                        if (document.activeElement === lastFocusable) {
                            e.preventDefault();
                            firstFocusable.focus();
                        }
                    }
                }
            });
        }



        async learnFromInteraction(message, response, feedback) {
            const interactionData = {
                message,
                response,
                feedback,
                context: {
                    timeStamp: Date.now(),
                    sessionDuration: this.getSessionDuration(),
                    previousMessages: this.messageHistory.slice(-5),
                    userBehavior: this.getUserBehaviorMetrics()
                }
            };

            try {
                await $.ajax({
                    url: qkbChatbot.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'qkb_learn_interaction',
                        nonce: qkbChatbot.nonce,
                        interaction: interactionData
                    }
                });

                // Update local ML patterns
                this.updateLocalPatterns(interactionData);

            } catch (error) {
                console.error('Learning error:', error);
            }
        }

        getUserBehaviorMetrics() {
            return {
                typingSpeed: this.calculateTypingSpeed(),
                correctionRate: this.calculateCorrectionRate(),
                responseTime: this.calculateResponseTime(),
                sessionEngagement: this.calculateSessionEngagement()
            };
        }



        getMLPattern(key) {
            try {
                return this.mlPatterns.get(key) || null;
            } catch (error) {
                console.warn('Error accessing ML pattern:', error);
                return null;
            }
        }

        adjustFullPageSize() {
            if (!this.isFullPage) return;

            const wrapper = this.$container.closest('.qkb-fullpage-wrapper');
            if (!wrapper.length) return;

            // Get wrapper dimensions
            const wrapperHeight = wrapper.height();
            const wrapperWidth = wrapper.width();

            // Apply responsive classes based on screen width
            if (wrapperWidth <= 480) {
                this.$container.addClass('qkb-mobile-view');
            } else {
                this.$container.removeClass('qkb-mobile-view');
            }

            // Update container dimensions
            this.$container.css({
                height: wrapperHeight,
                width: wrapperWidth
            });

            // Adjust messages container height
            const headerHeight = this.$container.find('.qkb-chatbot-header').outerHeight() || 0;
            const inputHeight = this.$container.find('.qkb-input-container').outerHeight() || 0;

            const messagesHeight = wrapperHeight - headerHeight - inputHeight;

            // Only set height if it's a positive value
            if (messagesHeight > 0) {
                this.$messages.css('height', messagesHeight + 'px');
            }

            // Scroll to bottom
            this.scrollToBottom(false);
        }


        debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        submitQuery(query) {
            this.showLoadingState(true);

            $.ajax({
                url: qkbChatbot.ajax_url,
                type: 'POST',
                data: {
                    action: 'qkb_get_completion',
                    nonce: qkbChatbot.nonce,
                    query: query,
                    context: this.getConversationContext()
                },
                success: (response) => {
                    this.handleResponse(response.data);
                    this.logInteraction(query, response.data);
                },
                error: (_, __, error) => {
                    this.showError('An error occurred. Please try again.');
                    console.error("API Error:", error);
                },
                complete: () => {
                    this.showLoadingState(false);
                }
            });
        }

        showToast(message, type = 'info') {
            const $toast = $(`
                <div class="qkb-toast qkb-toast-${type}">
                    <div class="qkb-toast-content">${message}</div>
                </div>
            `);

            $('body').append($toast);

            // Auto-remove after 3 seconds
            setTimeout(() => {
                $toast.fadeOut(500, () => $toast.remove());
            }, 3000);
        }

        showError(message) {
            // First try to add the error as a message
            try {
                this.addMessage(message || 'An error occurred. Please try again.', false, 'error');
            } catch (err) {
                // Fallback to toast if message adding fails
                console.error('Error showing error message:', err);
                this.showToast(message || 'An error occurred. Please try again.', 'error');
            }
        }

        async submitFeedback(messageId, feedback) {
            // Check if this feedback has already been submitted
            if (this.submittedFeedback && this.submittedFeedback.has(messageId)) {
                console.log('Feedback already submitted for message:', messageId);
                return;
            }

            // Initialize submitted feedback tracker if not exists
            if (!this.submittedFeedback) {
                this.submittedFeedback = new Set();
            }

            // Validate required fields
            if (!messageId || !this.lastQuery || !this.lastResponse) {
                console.error('Missing required fields for feedback submission:', {
                    messageId: messageId,
                    query: this.lastQuery,
                    response: this.lastResponse
                });
                this.showError('Unable to submit feedback: missing required information');
                return;
            }

            // Mark as submitted to prevent duplicates
            this.submittedFeedback.add(messageId);

            const data = {
                action: 'qkb_submit_feedback',
                nonce: qkbChatbot.nonce,
                message_id: messageId,
                feedback: feedback, // feedback is already 1 or -1 from the calling code
                query: this.lastQuery,
                response: this.lastResponse,
                assistant_id: this.currentAssistant || 1
            };

            $.ajax({
                url: qkbChatbot.ajax_url,
                type: 'POST',
                data: data,
                success: (response) => {
                    if (response.success) {
                        this.showToast(feedback > 0 ? 'Thanks for your positive feedback!' : 'Feedback recorded - we\'ll improve!');
                        console.log('Feedback submitted successfully');
                    } else {
                        console.error('Failed to submit feedback:', response.data);
                        this.showError('Failed to submit feedback: ' + (response.data || 'Unknown error'));
                    }
                },
                error: (xhr, status, error) => {
                    console.error('Feedback error:', {
                        status: status,
                        error: error,
                        response: xhr.responseText
                    });

                    // Remove from submitted feedback tracker on error so user can retry
                    if (this.submittedFeedback) {
                        this.submittedFeedback.delete(messageId);
                    }

                    this.showError('Failed to submit feedback. Please try again.');
                }
            });
        }

        /**
         * Change the current assistant
         *
         * @param {number} assistantId - The ID of the assistant to change to
         */
        changeAssistant(assistantId) {
            if (!assistantId || assistantId === this.currentAssistant) {
                return;
            }

            // Update the current assistant
            this.currentAssistant = assistantId;

            // Get default assistant ID
            const defaultAssistantId = (typeof qkbChatbot !== 'undefined' && qkbChatbot?.defaultassistant) || 0;

            // Update flag
            this.isUsingDefaultAssistant = (assistantId === parseInt(defaultAssistantId));

            // Get assistant info
            const ajaxUrl = (typeof qkbChatbot !== 'undefined' && qkbChatbot?.ajax_url) || '/wp-admin/admin-ajax.php';
            const nonce = (typeof qkbChatbot !== 'undefined' && qkbChatbot?.nonce) || '';

            $.ajax({
                url: ajaxUrl,
                type: 'POST',
                data: {
                    action: 'qkb_get_assistant_info',
                    nonce: nonce,
                    assistant_id: assistantId
                },
                success: (response) => {
                    if (response.success) {
                        // Update the assistant name in the header
                        $('.qkb-assistant-name').text(response.data.name);

                        // Clear the chat history
                        this.startNewChat();

                        // Show a system message about the assistant change
                        this.addMessage(`You are now chatting with ${response.data.name}. ${response.data.description}`, false);
                    }
                },
                error: (error) => {
                    console.error('Error changing assistant:', error);
                    this.showError('Failed to change assistant. Please try again.');
                }
            });
        }
    }

    // Polyfill for requestIdleCallback
    if (!window.requestIdleCallback) {
        window.requestIdleCallback = (callback, options) => {
            const start = Date.now();
            return setTimeout(() => {
                callback({
                    didTimeout: false,
                    timeRemaining: () => Math.max(0, 50 - (Date.now() - start))
                });
            }, options?.timeout || 1);
        };
    }





});
